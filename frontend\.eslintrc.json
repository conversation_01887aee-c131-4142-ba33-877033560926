{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": ["tsconfig.json"], "createDefaultProgram": true}, "extends": ["eslint:recommended"], "plugins": ["@typescript-eslint"], "env": {"browser": true, "node": true, "es6": true}, "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-empty-function": "warn", "no-console": ["warn", {"allow": ["warn", "error"]}], "no-debugger": "error", "no-duplicate-imports": "error", "no-unused-expressions": "error", "prefer-const": "error", "eqeqeq": ["error", "always"], "curly": "error"}}, {"files": ["*.spec.ts"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": ["tsconfig.spec.json"], "createDefaultProgram": true}, "extends": ["eslint:recommended"], "plugins": ["@typescript-eslint"], "env": {"browser": true, "node": true, "es6": true, "jasmine": true}, "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-empty-function": "warn", "no-console": ["warn", {"allow": ["warn", "error"]}], "no-debugger": "error", "no-duplicate-imports": "error", "no-unused-expressions": "error", "prefer-const": "error", "eqeqeq": ["error", "always"], "curly": "error"}}]}