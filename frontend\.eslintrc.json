{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "extends": ["eslint:recommended", "@typescript-eslint/recommended", "@angular-eslint/recommended", "@angular-eslint/template/process-inline-templates", "prettier"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "app", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "app", "style": "kebab-case"}], "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-empty-function": "warn", "@typescript-eslint/prefer-const": "error", "no-console": ["warn", {"allow": ["warn", "error"]}], "no-debugger": "error", "no-duplicate-imports": "error", "no-unused-expressions": "error", "prefer-const": "error", "eqeqeq": ["error", "always"], "curly": "error"}}, {"files": ["*.html"], "extends": ["@angular-eslint/template/recommended", "@angular-eslint/template/accessibility", "prettier"], "rules": {"@angular-eslint/template/no-negated-async": "error", "@angular-eslint/template/use-track-by-function": "warn"}}]}