<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="60" cy="60" r="60" fill="url(#avatarGradient)"/>
  
  <!-- Person icon -->
  <g transform="translate(60, 60)">
    <!-- Head -->
    <circle cx="0" cy="-15" r="18" fill="#1976d2" opacity="0.7"/>
    
    <!-- Body -->
    <path d="M -25 15 Q -25 -5 -15 -5 L 15 -5 Q 25 -5 25 15 L 25 35 L -25 35 Z" 
          fill="#1976d2" opacity="0.7"/>
  </g>
</svg>
