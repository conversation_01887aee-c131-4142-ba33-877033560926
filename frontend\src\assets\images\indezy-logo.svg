<svg width="120" height="40" viewBox="0 0 120 40" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976d2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#42a5f5;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Icon/Symbol -->
  <g transform="translate(5, 5)">
    <!-- Outer circle -->
    <circle cx="15" cy="15" r="14" fill="url(#logoGradient)" stroke="#ffffff" stroke-width="1"/>
    
    <!-- Inner design - representing freelance/project management -->
    <g transform="translate(15, 15)">
      <!-- Central dot -->
      <circle cx="0" cy="0" r="2" fill="#ffffff"/>
      
      <!-- Connected nodes representing projects/clients -->
      <circle cx="-8" cy="-4" r="1.5" fill="#ffffff" opacity="0.9"/>
      <circle cx="8" cy="-4" r="1.5" fill="#ffffff" opacity="0.9"/>
      <circle cx="-6" cy="6" r="1.5" fill="#ffffff" opacity="0.9"/>
      <circle cx="6" cy="6" r="1.5" fill="#ffffff" opacity="0.9"/>
      
      <!-- Connection lines -->
      <line x1="0" y1="0" x2="-8" y2="-4" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
      <line x1="0" y1="0" x2="8" y2="-4" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
      <line x1="0" y1="0" x2="-6" y2="6" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
      <line x1="0" y1="0" x2="6" y2="6" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
    </g>
  </g>
  
  <!-- Text -->
  <text x="40" y="25" font-family="'Roboto', sans-serif" font-size="18" font-weight="500" fill="#1976d2">
    Indezy
  </text>
</svg>
