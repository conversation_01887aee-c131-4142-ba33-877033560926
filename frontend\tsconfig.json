{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "sourceMap": true, "declaration": false, "experimentalDecorators": true, "moduleResolution": "bundler", "importHelpers": true, "target": "ES2022", "module": "ES2022", "useDefineForClassFields": false, "lib": ["ES2022", "dom"], "paths": {"@/*": ["src/*"], "@/components/*": ["src/app/components/*"], "@/services/*": ["src/app/services/*"], "@/models/*": ["src/app/models/*"], "@/guards/*": ["src/app/guards/*"], "@/utils/*": ["src/app/utils/*"]}}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}}