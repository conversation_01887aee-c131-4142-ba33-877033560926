{"Servers": {"1": {"Name": "Indezy Development Database", "Group": "Servers", "Host": "postgres", "Port": 5432, "MaintenanceDB": "indezy", "Username": "indezy_user", "PassFile": "/tmp/pgpassfile", "SSLMode": "prefer", "SSLCert": "<STORAGE_DIR>/.postgresql/postgresql.crt", "SSLKey": "<STORAGE_DIR>/.postgresql/postgresql.key", "SSLCompression": 0, "Timeout": 10, "UseSSHTunnel": 0, "TunnelHost": "", "TunnelPort": "22", "TunnelUsername": "", "TunnelAuthentication": 0}}}