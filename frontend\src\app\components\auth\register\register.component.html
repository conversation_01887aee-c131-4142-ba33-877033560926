<div class="register-container">
  <div class="register-card-container">
    <mat-card class="register-card">
      <mat-card-header>
        <div class="register-header">
          <img src="assets/images/indezy-logo.svg" alt="Indezy" class="register-logo" height="48">
          <mat-card-title class="register-title">
            Créer un compte Indezy
          </mat-card-title>
          <mat-card-subtitle>
            Rejoignez la communauté des freelances organisés
          </mat-card-subtitle>
        </div>
      </mat-card-header>

      <mat-card-content>
        <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">
          <div class="name-row">
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Prénom</mat-label>
              <input matInput 
                     type="text" 
                     formControlName="firstName" 
                     placeholder="Jean"
                     autocomplete="given-name">
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched">
                {{ getErrorMessage('firstName') }}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Nom</mat-label>
              <input matInput 
                     type="text" 
                     formControlName="lastName" 
                     placeholder="Dupont"
                     autocomplete="family-name">
              <mat-error *ngIf="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched">
                {{ getErrorMessage('lastName') }}
              </mat-error>
            </mat-form-field>
          </div>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Email</mat-label>
            <input matInput 
                   type="email" 
                   formControlName="email" 
                   placeholder="<EMAIL>"
                   autocomplete="email">
            <mat-icon matSuffix>email</mat-icon>
            <mat-error *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched">
              {{ getErrorMessage('email') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Mot de passe</mat-label>
            <input matInput 
                   [type]="hidePassword ? 'password' : 'text'" 
                   formControlName="password"
                   autocomplete="new-password">
            <button mat-icon-button 
                    matSuffix 
                    type="button"
                    (click)="hidePassword = !hidePassword"
                    [attr.aria-label]="'Hide password'"
                    [attr.aria-pressed]="hidePassword">
              <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
            <mat-error *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched">
              {{ getErrorMessage('password') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Confirmer le mot de passe</mat-label>
            <input matInput 
                   [type]="hideConfirmPassword ? 'password' : 'text'" 
                   formControlName="confirmPassword"
                   autocomplete="new-password">
            <button mat-icon-button 
                    matSuffix 
                    type="button"
                    (click)="hideConfirmPassword = !hideConfirmPassword"
                    [attr.aria-label]="'Hide confirm password'"
                    [attr.aria-pressed]="hideConfirmPassword">
              <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
            <mat-error *ngIf="registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched">
              {{ getErrorMessage('confirmPassword') }}
            </mat-error>
          </mat-form-field>

          <button mat-raised-button 
                  color="primary" 
                  type="submit" 
                  class="register-button full-width"
                  [disabled]="registerForm.invalid || isLoading">
            <mat-spinner *ngIf="isLoading" diameter="20" class="register-spinner"></mat-spinner>
            <span *ngIf="!isLoading">Créer mon compte</span>
            <span *ngIf="isLoading">Création...</span>
          </button>
        </form>
      </mat-card-content>

      <mat-card-actions class="register-actions">
        <p class="login-link">
          Déjà un compte ? 
          <a routerLink="/login" class="login-link-text">Se connecter</a>
        </p>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
