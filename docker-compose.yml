version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: indezy-postgres
    environment:
      POSTGRES_DB: indezy
      POSTGRES_USER: indezy_user
      POSTGRES_PASSWORD: indezy_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - indezy-network

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: indezy-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - indezy-network

volumes:
  postgres_data:

networks:
  indezy-network:
    driver: bridge
