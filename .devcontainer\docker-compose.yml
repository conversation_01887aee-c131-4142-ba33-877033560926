version: '3.8'

services:
  devcontainer:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ../..:/workspace:cached
      - maven-cache:/home/<USER>/.m2
      - node-cache:/home/<USER>/.npm
      - vscode-extensions:/home/<USER>/.vscode-server/extensions
    command: sleep infinity
    networks:
      - indezy-dev-network
    depends_on:
      postgres:
        condition: service_healthy
    env_file:
      - .env
    environment:
      - SPRING_PROFILES_ACTIVE=devcontainer

  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    env_file:
      - .env
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ../database/init:/docker-entrypoint-initdb.d:ro
    networks:
      - indezy-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U indezy_user -d indezy"]
      interval: 10s
      timeout: 5s
      retries: 5

  pgadmin:
    image: dpage/pgadmin4:latest
    restart: unless-stopped
    env_file:
      - .env
    ports:
      - "5050:80"
    volumes:
      - pgadmin-data:/var/lib/pgadmin
      - ./pgadmin-servers.json:/pgadmin4/servers.json:ro
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - indezy-dev-network

volumes:
  postgres-data:
  pgadmin-data:
  maven-cache:
  node-cache:
  vscode-extensions:

networks:
  indezy-dev-network:
    driver: bridge
