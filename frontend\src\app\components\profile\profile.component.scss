.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.profile-header {
  margin-bottom: 32px;
  text-align: center;

  h1 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin: 0 0 8px 0;
    font-size: 2rem;
    font-weight: 500;
    color: var(--mat-primary-500);

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }
  }

  .profile-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin: 0;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 24px;
  gap: 16px;

  p {
    color: var(--text-secondary);
    margin: 0;
  }
}

.profile-content {
  .profile-tabs {
    .mat-mdc-tab-group {
      --mat-tab-active-indicator-color: var(--mat-primary-500);
    }

    .mat-mdc-tab-header {
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }
  }
}

.tab-content {
  padding: 24px 0;
}

.avatar-section {
  margin-bottom: 24px;

  .avatar-container {
    display: flex;
    align-items: center;
    gap: 24px;
    padding: 24px;

    .avatar-wrapper {
      position: relative;
      
      .user-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid var(--mat-primary-100);
      }

      .avatar-overlay {
        position: absolute;
        bottom: 0;
        right: 0;
        
        .avatar-edit-btn {
          background-color: var(--mat-primary-500);
          color: white;
          
          &:hover {
            background-color: var(--mat-primary-600);
          }
        }
      }
    }

    .avatar-info {
      h3 {
        margin: 0 0 8px 0;
        font-size: 1.5rem;
        font-weight: 500;
      }

      p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 1.1rem;
      }
    }
  }
}

.form-section {
  margin-bottom: 24px;

  mat-card-header {
    margin-bottom: 16px;
  }

  mat-card-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--text-primary);
  }
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;

  mat-form-field {
    flex: 1;
  }
}

.full-width {
  width: 100%;
}

.skills-section,
.languages-section {
  margin-top: 24px;

  h4 {
    margin: 0 0 16px 0;
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-primary);
  }

  .chips-container {
    margin-bottom: 16px;

    mat-chip-set {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    mat-chip {
      background-color: var(--mat-primary-100);
      color: var(--mat-primary-700);
    }
  }

  .skill-input,
  .language-input {
    max-width: 300px;
  }
}

.toggle-section {
  margin-top: 16px;

  mat-slide-toggle {
    margin-bottom: 16px;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);

  button {
    min-width: 120px;
    
    mat-spinner {
      margin-right: 8px;
    }
  }
}

.notification-settings {
  .notification-item {
    padding: 16px 0;

    mat-slide-toggle {
      margin-bottom: 8px;
    }

    .notification-description {
      margin: 0;
      color: var(--text-secondary);
      font-size: 0.9rem;
      line-height: 1.4;
    }
  }

  mat-divider {
    margin: 8px 0;
  }
}

.account-actions {
  .action-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 0;

    .action-info {
      flex: 1;

      h4 {
        margin: 0 0 8px 0;
        font-size: 1.1rem;
        font-weight: 500;
      }

      p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 0.9rem;
        line-height: 1.4;
      }
    }

    button {
      min-width: 120px;
      
      mat-icon {
        margin-right: 8px;
      }
    }

    &.danger {
      .action-info h4 {
        color: var(--mat-warn-500);
      }
    }
  }

  mat-divider {
    margin: 16px 0;
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
  }

  .avatar-container {
    flex-direction: column;
    text-align: center;
    gap: 16px !important;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .action-item {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 16px;

    button {
      align-self: stretch;
    }
  }

  .form-actions {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .profile-container {
    --text-primary: rgba(255, 255, 255, 0.87);
    --text-secondary: rgba(255, 255, 255, 0.6);
  }
}

// Animation for loading states
.loading-container mat-spinner {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Custom scrollbar for tab content
.tab-content {
  scrollbar-width: thin;
  scrollbar-color: var(--mat-primary-300) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--mat-primary-300);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: var(--mat-primary-400);
  }
}
