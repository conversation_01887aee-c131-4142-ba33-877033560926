.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.app-toolbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;

  .app-logo {
    height: 32px;
    width: auto;
    margin-left: 8px;
  }

  .toolbar-title {
    font-size: 1.5rem;
    font-weight: 600;
  }

  .spacer {
    flex: 1 1 auto;
  }

  .user-button {
    display: flex;
    align-items: center;
    gap: 8px;

    .user-name {
      font-weight: 500;
    }
  }
}

.app-sidenav-container {
  flex: 1;
  margin-top: 64px; // Height of toolbar
}

.app-sidenav {
  width: 280px;
  border-right: 1px solid rgba(0, 0, 0, 0.12);

  .mat-mdc-nav-list {
    padding-top: 16px;
  }

  .mat-mdc-list-item {
    margin: 4px 12px;
    border-radius: 8px;

    &.active-nav-item {
      background-color: rgba(var(--mat-primary-500), 0.1);
      color: var(--mat-primary-500);

      .mat-icon {
        color: var(--mat-primary-500);
      }
    }

    &:hover:not(.active-nav-item) {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

.app-main-content {
  background-color: #f5f5f5;
}

.app-content {
  padding: 24px;
  min-height: calc(100vh - 64px);
}

// Mobile responsive
@media (max-width: 768px) {
  .app-sidenav {
    width: 100%;
  }

  .app-content {
    padding: 16px;
  }
}
