<div class="contact-list-container">
  <mat-card class="header-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>contacts</mat-icon>
        Gestion des Contacts
      </mat-card-title>
      <mat-card-subtitle>
        <PERSON><PERSON><PERSON> vos contacts clients et leurs informations
      </mat-card-subtitle>
    </mat-card-header>
  </mat-card>

  <mat-card class="filters-card">
    <mat-card-content>
      <div class="actions-row">
        <div class="search-filters">
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Rechercher un contact</mat-label>
            <input matInput [(ngModel)]="searchQuery"
                   (input)="onSearchChange($any($event.target).value || '')"
                   placeholder="Nom, email, poste, entreprise...">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Statut</mat-label>
            <mat-select [(ngModel)]="selectedStatus" (selectionChange)="onStatusFilterChange()">
              <mat-option value="">Tous les statuts</mat-option>
              <mat-option value="ACTIVE">Actif</mat-option>
              <mat-option value="INACTIVE">Inactif</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Client</mat-label>
            <mat-select [(ngModel)]="selectedClient" (selectionChange)="onClientFilterChange()">
              <mat-option value="">Tous les clients</mat-option>
              <mat-option *ngFor="let client of clients" [value]="client.id.toString()">
                {{ client.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <button mat-button (click)="clearFilters()" class="clear-filters-btn">
            <mat-icon>clear</mat-icon>
            Effacer les filtres
          </button>
        </div>

        <div class="action-buttons">
          <button mat-raised-button color="primary" (click)="onCreate()">
            <mat-icon>add</mat-icon>
            Nouveau Contact
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <mat-card class="table-card">
    <mat-card-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Chargement des contacts...</p>
      </div>

      <div *ngIf="!isLoading" class="table-container">
        <table mat-table [dataSource]="filteredContacts" class="contacts-table">
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>Nom</th>
            <td mat-cell *matCellDef="let contact">
              <div class="contact-name">
                <strong>{{ getFullName(contact) }}</strong>
              </div>
            </td>
          </ng-container>

          <!-- Email Column -->
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef>Email</th>
            <td mat-cell *matCellDef="let contact">
              <a [href]="'mailto:' + contact.email" class="email-link">
                {{ contact.email }}
              </a>
            </td>
          </ng-container>

          <!-- Phone Column -->
          <ng-container matColumnDef="phone">
            <th mat-header-cell *matHeaderCellDef>Téléphone</th>
            <td mat-cell *matCellDef="let contact">
              <a [href]="'tel:' + contact.phone" class="phone-link">
                {{ contact.phone }}
              </a>
            </td>
          </ng-container>

          <!-- Position Column -->
          <ng-container matColumnDef="position">
            <th mat-header-cell *matHeaderCellDef>Poste</th>
            <td mat-cell *matCellDef="let contact">
              {{ contact.position }}
            </td>
          </ng-container>

          <!-- Client Column -->
          <ng-container matColumnDef="client">
            <th mat-header-cell *matHeaderCellDef>Client</th>
            <td mat-cell *matCellDef="let contact">
              <div class="client-info">
                {{ contact.clientName }}
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Statut</th>
            <td mat-cell *matCellDef="let contact">
              <mat-chip [color]="getStatusColor(contact.status)" selected>
                {{ getStatusLabel(contact.status) }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let contact">
              <div class="action-buttons">
                <button mat-icon-button (click)="onView(contact)" matTooltip="Voir les détails">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-icon-button (click)="onEdit(contact)" matTooltip="Modifier">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button color="warn" (click)="onDelete(contact)" matTooltip="Supprimer">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="contact-row"></tr>
        </table>

        <div *ngIf="filteredContacts.length === 0" class="no-data">
          <mat-icon>contacts</mat-icon>
          <h3>Aucun contact trouvé</h3>
          <p>{{ searchQuery || selectedStatus || selectedClient ? 'Aucun contact ne correspond aux critères de recherche.' : 'Commencez par ajouter votre premier contact.' }}</p>
          <button mat-raised-button color="primary" (click)="onCreate()" *ngIf="!searchQuery && !selectedStatus && !selectedClient">
            <mat-icon>add</mat-icon>
            Ajouter un contact
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
