/* You can add global styles to this file, and also import other style files */

html, body { height: 100%; }
body { margin: 0; font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif; }

/* Custom Material Theme Colors */
:root {
  --primary-color: #1976d2;
  --accent-color: #ff4081;
  --warn-color: #f44336;
  --success-color: #4caf50;
  --background-color: #fafafa;
  --surface-color: #ffffff;
  --text-primary: rgba(0, 0, 0, 0.87);
  --text-secondary: rgba(0, 0, 0, 0.54);
}

/* Global utility classes */
.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

/* Loading spinner */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Card styles */
.mat-mdc-card {
  margin: 16px;
}

/* Form styles */
.form-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 24px;
}

.form-field {
  width: 100%;
  margin-bottom: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .mat-mdc-card {
    margin: 8px;
  }
  
  .form-container {
    padding: 16px;
  }
}
