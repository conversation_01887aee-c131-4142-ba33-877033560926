{"name": "Indezy Development Environment", "dockerComposeFile": "docker-compose.yml", "service": "devcontainer", "workspaceFolder": "/workspace", "customizations": {"vscode": {"extensions": ["vscjava.vscode-java-pack", "angular.ng-template", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-azuretools.vscode-docker", "GitLab.gitlab-workflow", "ms-vscode.vscode-postgresql"], "settings": {"java.configuration.runtimes": [{"name": "JavaSE-21", "path": "/usr/local/sdkman/candidates/java/current"}], "java.compile.nullAnalysis.mode": "automatic", "java.configuration.maven.userSettings": "/workspace/.devcontainer/maven-settings.xml"}}}, "forwardPorts": [8080, 4200, 5432, 5050], "portsAttributes": {"8080": {"label": "Spring Boot Backend", "onAutoForward": "notify"}, "4200": {"label": "Angular Frontend", "onAutoForward": "openBrowser"}, "5432": {"label": "PostgreSQL Database", "onAutoForward": "silent"}, "5050": {"label": "pgAdmin", "onAutoForward": "notify"}}, "postCreateCommand": "bash .devcontainer/post-create.sh", "remoteUser": "vscode", "shutdownAction": "stopCompose"}