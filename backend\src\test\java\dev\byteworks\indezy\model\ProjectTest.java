package dev.byteworks.indezy.model;

import dev.byteworks.indezy.model.enums.EmploymentStatus;
import dev.byteworks.indezy.model.enums.WorkMode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.assertj.core.api.Assertions.*;

class ProjectTest {

    private Project project;
    private Freelance freelance;
    private Client client;

    @BeforeEach
    void setUp() {
        // Setup freelance
        freelance = new Freelance();
        freelance.setId(1L);
        freelance.setFirstName("John");
        freelance.setLastName("Doe");
        freelance.setEmail("<EMAIL>");
        freelance.setStatus(EmploymentStatus.FREELANCE);

        // Setup client
        client = new Client();
        client.setId(1L);
        client.setCompanyName("Test Company");
        client.setCity("Paris");
        client.setIsFinal(true);
        client.setFreelance(freelance);

        // Setup project
        project = new Project();
        project.setId(1L);
        project.setRole("Full Stack Developer");
        project.setDescription("Test project description");
        project.setTechStack("Java, Spring Boot, Angular");
        project.setDailyRate(600);
        project.setWorkMode(WorkMode.HYBRID);
        project.setRemoteDaysPerMonth(15);
        project.setOnsiteDaysPerMonth(5);
        project.setStartDate(LocalDate.of(2024, 1, 15));
        project.setDurationInMonths(6);
        project.setDaysPerYear(220);
        project.setPersonalRating(4);
        project.setFreelance(freelance);
        project.setClient(client);
    }

    @Test
    void getTotalRevenue_WithAllRequiredFields_ShouldCalculateCorrectly() {
        // When
        Integer totalRevenue = project.getTotalRevenue();

        // Then
        // Expected: 600 * 220 * 6 / 12 = 66,000
        assertThat(totalRevenue).isEqualTo(66000);
    }

    @Test
    void getTotalRevenue_WithNullDailyRate_ShouldReturnNull() {
        // Given
        project.setDailyRate(null);

        // When
        Integer totalRevenue = project.getTotalRevenue();

        // Then
        assertThat(totalRevenue).isNull();
    }

    @Test
    void getTotalRevenue_WithNullDaysPerYear_ShouldReturnNull() {
        // Given
        project.setDaysPerYear(null);

        // When
        Integer totalRevenue = project.getTotalRevenue();

        // Then
        assertThat(totalRevenue).isNull();
    }

    @Test
    void getTotalRevenue_WithNullDurationInMonths_ShouldReturnNull() {
        // Given
        project.setDurationInMonths(null);

        // When
        Integer totalRevenue = project.getTotalRevenue();

        // Then
        assertThat(totalRevenue).isNull();
    }

    @Test
    void getTotalRevenue_WithZeroDaysPerYear_ShouldReturnZero() {
        // Given
        project.setDaysPerYear(0);

        // When
        Integer totalRevenue = project.getTotalRevenue();

        // Then
        assertThat(totalRevenue).isEqualTo(0);
    }

    @Test
    void getTotalRevenue_WithZeroDurationInMonths_ShouldReturnZero() {
        // Given
        project.setDurationInMonths(0);

        // When
        Integer totalRevenue = project.getTotalRevenue();

        // Then
        assertThat(totalRevenue).isEqualTo(0);
    }

    @Test
    void getTotalRevenue_WithDifferentValues_ShouldCalculateCorrectly() {
        // Given
        project.setDailyRate(500);
        project.setDaysPerYear(200);
        project.setDurationInMonths(12);

        // When
        Integer totalRevenue = project.getTotalRevenue();

        // Then
        // Expected: 500 * 200 * 12 / 12 = 100,000
        assertThat(totalRevenue).isEqualTo(100000);
    }

    @Test
    void getTotalRevenue_WithPartialYear_ShouldCalculateCorrectly() {
        // Given
        project.setDailyRate(800);
        project.setDaysPerYear(240);
        project.setDurationInMonths(3);

        // When
        Integer totalRevenue = project.getTotalRevenue();

        // Then
        // Expected: 800 * 240 * 3 / 12 = 48,000
        assertThat(totalRevenue).isEqualTo(48000);
    }

    @Test
    void project_GettersAndSetters_ShouldWorkCorrectly() {
        // Given
        String role = "Backend Developer";
        String description = "New description";
        String techStack = "Python, Django";
        Integer dailyRate = 700;
        WorkMode workMode = WorkMode.REMOTE;
        Integer remoteDays = 20;
        Integer onsiteDays = 0;
        String advantages = "Flexible hours";
        LocalDate startDate = LocalDate.of(2024, 6, 1);
        Integer duration = 8;
        Integer renewal = 4;
        Integer daysPerYear = 200;
        String link = "https://project.example.com";
        Integer rating = 5;
        String notes = "Excellent project";

        // When
        project.setRole(role);
        project.setDescription(description);
        project.setTechStack(techStack);
        project.setDailyRate(dailyRate);
        project.setWorkMode(workMode);
        project.setRemoteDaysPerMonth(remoteDays);
        project.setOnsiteDaysPerMonth(onsiteDays);
        project.setAdvantages(advantages);
        project.setStartDate(startDate);
        project.setDurationInMonths(duration);
        project.setOrderRenewalInMonths(renewal);
        project.setDaysPerYear(daysPerYear);
        project.setLink(link);
        project.setPersonalRating(rating);
        project.setNotes(notes);

        // Then
        assertThat(project.getRole()).isEqualTo(role);
        assertThat(project.getDescription()).isEqualTo(description);
        assertThat(project.getTechStack()).isEqualTo(techStack);
        assertThat(project.getDailyRate()).isEqualTo(dailyRate);
        assertThat(project.getWorkMode()).isEqualTo(workMode);
        assertThat(project.getRemoteDaysPerMonth()).isEqualTo(remoteDays);
        assertThat(project.getOnsiteDaysPerMonth()).isEqualTo(onsiteDays);
        assertThat(project.getAdvantages()).isEqualTo(advantages);
        assertThat(project.getStartDate()).isEqualTo(startDate);
        assertThat(project.getDurationInMonths()).isEqualTo(duration);
        assertThat(project.getOrderRenewalInMonths()).isEqualTo(renewal);
        assertThat(project.getDaysPerYear()).isEqualTo(daysPerYear);
        assertThat(project.getLink()).isEqualTo(link);
        assertThat(project.getPersonalRating()).isEqualTo(rating);
        assertThat(project.getNotes()).isEqualTo(notes);
    }

    @Test
    void project_Relationships_ShouldWorkCorrectly() {
        // Then
        assertThat(project.getFreelance()).isEqualTo(freelance);
        assertThat(project.getClient()).isEqualTo(client);
        assertThat(project.getFreelance().getId()).isEqualTo(1L);
        assertThat(project.getClient().getId()).isEqualTo(1L);
        assertThat(project.getFreelance().getFirstName()).isEqualTo("John");
        assertThat(project.getClient().getCompanyName()).isEqualTo("Test Company");
    }

    @Test
    void project_WithSource_ShouldSetSourceCorrectly() {
        // Given
        Source source = new Source();
        source.setId(1L);
        source.setName("LinkedIn");
        source.setFreelance(freelance);

        // When
        project.setSource(source);

        // Then
        assertThat(project.getSource()).isEqualTo(source);
        assertThat(project.getSource().getName()).isEqualTo("LinkedIn");
    }

    @Test
    void project_WithMiddleman_ShouldSetMiddlemanCorrectly() {
        // Given
        Client middleman = new Client();
        middleman.setId(2L);
        middleman.setCompanyName("Consulting Agency");
        middleman.setCity("Lyon");
        middleman.setIsFinal(false);
        middleman.setFreelance(freelance);

        // When
        project.setMiddleman(middleman);

        // Then
        assertThat(project.getMiddleman()).isEqualTo(middleman);
        assertThat(project.getMiddleman().getCompanyName()).isEqualTo("Consulting Agency");
        assertThat(project.getMiddleman().getIsFinal()).isFalse();
    }

    @Test
    void project_InheritedFromBaseEntity_ShouldHaveBaseFields() {
        // Then
        assertThat(project.getId()).isEqualTo(1L);
        // Note: createdAt, updatedAt, and version are managed by JPA and would be null in unit tests
        // These would be tested in integration tests with actual JPA context
    }

    @Test
    void project_WorkModeEnum_ShouldWorkCorrectly() {
        // Test all work modes
        project.setWorkMode(WorkMode.REMOTE);
        assertThat(project.getWorkMode()).isEqualTo(WorkMode.REMOTE);

        project.setWorkMode(WorkMode.ONSITE);
        assertThat(project.getWorkMode()).isEqualTo(WorkMode.ONSITE);

        project.setWorkMode(WorkMode.HYBRID);
        assertThat(project.getWorkMode()).isEqualTo(WorkMode.HYBRID);
    }

    @Test
    void project_ValidationConstraints_ShouldBePresent() {
        // This test verifies that validation annotations are present
        // The actual validation would be tested in integration tests or with a validator
        
        // Test that required fields can be set
        project.setRole("Required Role");
        project.setDailyRate(500);
        
        assertThat(project.getRole()).isNotNull();
        assertThat(project.getDailyRate()).isNotNull();
    }
}
