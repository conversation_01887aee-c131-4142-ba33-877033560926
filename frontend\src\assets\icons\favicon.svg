<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="faviconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976d2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#42a5f5;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#faviconGradient)" stroke="#ffffff" stroke-width="1"/>
  
  <!-- Inner design - representing freelance/project management -->
  <g transform="translate(16, 16)">
    <!-- Central dot -->
    <circle cx="0" cy="0" r="2" fill="#ffffff"/>
    
    <!-- Connected nodes representing projects/clients -->
    <circle cx="-8" cy="-4" r="1.5" fill="#ffffff" opacity="0.9"/>
    <circle cx="8" cy="-4" r="1.5" fill="#ffffff" opacity="0.9"/>
    <circle cx="-6" cy="6" r="1.5" fill="#ffffff" opacity="0.9"/>
    <circle cx="6" cy="6" r="1.5" fill="#ffffff" opacity="0.9"/>
    
    <!-- Connection lines -->
    <line x1="0" y1="0" x2="-8" y2="-4" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
    <line x1="0" y1="0" x2="8" y2="-4" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
    <line x1="0" y1="0" x2="-6" y2="6" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
    <line x1="0" y1="0" x2="6" y2="6" stroke="#ffffff" stroke-width="1" opacity="0.7"/>
  </g>
</svg>
