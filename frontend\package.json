{"name": "indezy-frontend", "version": "1.0.0", "description": "Job tracking application for freelancers - Frontend", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4200", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^20.0.6", "@angular/cdk": "^20.0.5", "@angular/cdk-experimental": "^20.0.5", "@angular/common": "^20.0.6", "@angular/compiler": "^20.0.6", "@angular/core": "^20.0.6", "@angular/forms": "^20.0.6", "@angular/material": "^20.0.5", "@angular/platform-browser": "^20.0.6", "@angular/platform-browser-dynamic": "^20.0.6", "@angular/router": "^20.0.6", "@angular/service-worker": "^20.0.6", "angular-oauth2-oidc": "^20.0.2", "rxjs": "~7.8.2", "tslib": "^2.8.1", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-eslint/eslint-plugin": "^20.1.1", "@angular-eslint/eslint-plugin-template": "^20.1.1", "@angular-eslint/template-parser": "^20.1.1", "@angular/build": "^20.0.5", "@angular/cli": "^20.0.5", "@angular/compiler-cli": "^20.0.6", "@types/jasmine": "~5.1.5", "@types/node": "^24.0.10", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.1", "jasmine-core": "~5.8.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.3"}}